'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Camera, Search, RefreshCw, Gift, ArrowRight } from 'lucide-react';

const steps = [
  {
    icon: Camera,
    title: 'List Your Items',
    description: 'Take photos of your pre-loved clothes and create detailed listings with descriptions, sizes, and condition.',
    details: ['High-quality photos', 'Detailed descriptions', 'Size and condition info', 'Set your preferences'],
  },
  {
    icon: Search,
    title: 'Discover & Browse',
    description: 'Explore thousands of unique pieces from other users. Use filters to find exactly what you\'re looking for.',
    details: ['Smart search filters', 'Category browsing', 'Wishlist creation', 'AI recommendations'],
  },
  {
    icon: RefreshCw,
    title: 'Exchange & Swap',
    description: 'Connect with other users to arrange swaps using Pedi tokens or direct exchanges. Safe and secure transactions.',
    details: ['Token-based exchanges', 'Direct swaps', 'Secure messaging', 'M-Pesa integration'],
  },
  {
    icon: Gift,
    title: 'Earn & Give Back',
    description: 'Earn Pedi tokens for every sustainable action and donate clothes to local charities to make a positive impact.',
    details: ['Earn tokens for actions', 'Charity donations', 'Community impact', 'Environmental tracking'],
  },
];

export default function HowItWorks() {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleItems(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('[data-index]');
    elements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section id="how-it-works" ref={sectionRef} className="py-20 bg-brand-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16" data-index="0">
          <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-heading text-brand-dark-green mb-6 transition-all duration-1000 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            How{' '}
            <span className="bg-gradient-to-r from-brand-pine-green via-brand-sage-green to-brand-forest-green bg-clip-text text-transparent font-bold">
              Pedi
            </span>{' '}
            Works
          </h2>
          <p className={`text-xl font-body text-brand-warm-gray max-w-3xl mx-auto transition-all duration-1000 delay-200 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Simple steps to start your sustainable fashion journey
          </p>
        </div>

        {/* Enhanced Steps Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left side - Enhanced Steps */}
          <div className="space-modern">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = activeStep === index;
              
              return (
                <div
                  key={index}
                  data-index={index + 1}
                  className={`card-modern flex items-start space-x-6 p-8 transition-all duration-1000 delay-${(index + 1) * 200} cursor-pointer hover-glow click-scale ${
                    visibleItems.includes(index + 1) ? 'animate-fade-in-left opacity-100' : 'opacity-0'
                  } ${isActive ? 'bg-gradient-soft border-brand-pine-green/50 shadow-glow' : 'hover:bg-brand-light-green/30 border-brand-sage-green/30'}`}
                  onClick={() => setActiveStep(index)}
                >
                  <div className={`w-18 h-18 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 shadow-soft hover-bounce ${
                    isActive
                      ? 'bg-gradient-primary text-brand-white scale-110 shadow-glow'
                      : 'bg-brand-light-green text-brand-dark-green hover:bg-brand-sage-green hover:text-brand-white'
                  }`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-4">
                      <span className={`text-sm font-subheading px-4 py-2 rounded-full transition-all duration-300 ${
                        isActive
                          ? 'bg-gradient-primary text-brand-white shadow-colored'
                          : 'bg-brand-light-green text-brand-dark-green'
                      }`}>
                        Step {index + 1}
                      </span>
                      <h3 className={`text-xl font-heading transition-all duration-300 ${
                        isActive ? 'text-brand-pine-green' : 'text-brand-dark-green'
                      }`}>
                        {step.title}
                      </h3>
                    </div>

                    <p className="text-brand-charcoal/80 mb-4 leading-relaxed font-body">
                      {step.description}
                    </p>

                    {isActive && (
                      <ul className="space-y-3 animate-fade-in-up">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center space-x-3 text-sm text-brand-charcoal/70">
                            <ArrowRight className="h-4 w-4 text-brand-pine-green" />
                            <span className="font-body">{detail}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Right side - Enhanced Visual */}
          <div data-index="5" className={`transition-all duration-1000 delay-600 ${
            visibleItems.includes(5) ? 'animate-fade-in-right opacity-100' : 'opacity-0'
          }`}>
            <div className="relative">
              {/* Enhanced main visual container */}
              <div className="bg-gradient-hero rounded-3xl p-10 text-brand-white shadow-elevated border border-brand-sage-green/20 card-elevated">
                <div className="text-center mb-10">
                  <h3 className="text-2xl font-heading mb-4">
                    {steps[activeStep].title}
                  </h3>
                  <p className="text-brand-white/90 font-body">
                    {steps[activeStep].description}
                  </p>
                </div>

                {/* Enhanced step indicator */}
                <div className="flex justify-center space-x-3 mb-8">
                  {steps.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveStep(index)}
                      className={`w-4 h-4 rounded-full transition-all duration-300 hover-scale click-scale ${
                        activeStep === index
                          ? 'bg-brand-sage-green scale-125 shadow-glow'
                          : 'bg-brand-white/30 hover:bg-brand-white/50'
                      }`}
                    />
                  ))}
                </div>

                {/* Enhanced animated icon */}
                <div className="text-center">
                  <div className="w-28 h-28 card-glass rounded-2xl flex items-center justify-center mx-auto animate-scale-in shadow-glow border border-brand-white/20">
                    {React.createElement(steps[activeStep].icon, {
                      className: "h-14 w-14 text-brand-sage-green"
                    })}
                  </div>
                </div>
              </div>

              {/* Enhanced floating elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-brand-sage-green rounded-full animate-float shadow-glow"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-brand-mint rounded-full animate-float shadow-colored" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-1/2 -right-2 w-4 h-4 bg-brand-light-green rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
            </div>
          </div>
        </div>

        {/* Enhanced progress bar */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-brand-light-green rounded-full h-3 mb-4 shadow-inner">
            <div
              className="bg-gradient-primary h-3 rounded-full transition-all duration-1000 shadow-glow"
              style={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
          <p className="text-center text-brand-charcoal text-sm font-body">
            Step {activeStep + 1} of {steps.length}
          </p>
        </div>
      </div>
    </section>
  );
}