'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import BulkDonationForm from '@/components/donations/BulkDonationForm';
import { 
  Heart, 
  Scale, 
  Coins, 
  TrendingUp,
  Calendar,
  MapPin,
  Package,
  Award,
  Users,
  Building
} from 'lucide-react';

interface DonationStats {
  totalWeight: number;
  totalDonations: number;
  tokensEarned: number;
  impactScore: number;
  rank: string;
}

interface Donation {
  _id: string;
  type: 'charity' | 'recycling';
  weight: number;
  tokensEarned: number;
  partner: {
    name: string;
    location: string;
  };
  status: 'pending' | 'collected' | 'completed';
  createdAt: string;
}

interface CharityPartner {
  _id: string;
  name: string;
  description: string;
  location: string;
  totalDonations: number;
  rewardRate: number;
}

export default function DonationsPage() {
  const [stats, setStats] = useState<DonationStats>({
    totalWeight: 0,
    totalDonations: 0,
    tokensEarned: 0,
    impactScore: 0,
    rank: 'Beginner'
  });
  const [donations, setDonations] = useState<Donation[]>([]);
  const [partners, setPartners] = useState<CharityPartner[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDonationData();
  }, []);

  const fetchDonationData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Fetch donation stats
      const statsResponse = await fetch('/api/donations/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      // Fetch donation history
      const historyResponse = await fetch('/api/donations/history', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      // Fetch charity partners
      const partnersResponse = await fetch('/api/charity-partners');

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data || stats);
      }

      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setDonations(historyData.data || []);
      }

      if (partnersResponse.ok) {
        const partnersData = await partnersResponse.json();
        setPartners(partnersData.data || []);
      }
    } catch (error) {
      console.error('Error fetching donation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'collected': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Donations</h1>
          <p className="text-gray-600">Donate clothing to charity and earn Pedi tokens</p>
        </div>
        <div className="text-right">
          <div className="flex items-center gap-2 text-2xl font-bold text-red-600">
            <Heart className="h-6 w-6" />
            {stats.totalDonations}
          </div>
          <p className="text-sm text-gray-500">Total Donations</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWeight} kg</div>
            <p className="text-xs text-muted-foreground">Clothing donated</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tokens Earned</CardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.tokensEarned}</div>
            <p className="text-xs text-muted-foreground">From donations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Impact Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.impactScore}</div>
            <p className="text-xs text-muted-foreground">Environmental impact</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Donor Rank</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.rank}</div>
            <p className="text-xs text-muted-foreground">Current level</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="donate" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="donate">Make Donation</TabsTrigger>
          <TabsTrigger value="history">Donation History</TabsTrigger>
          <TabsTrigger value="partners">Charity Partners</TabsTrigger>
        </TabsList>

        <TabsContent value="donate" className="space-y-6">
          <BulkDonationForm onSuccess={fetchDonationData} />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Your Donation History</CardTitle>
              <CardDescription>Track your past donations and their impact</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : donations.length === 0 ? (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No donations yet. Start making a difference!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {donations.map((donation) => (
                    <div key={donation._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-red-100 rounded-full">
                          <Heart className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium">{donation.partner.name}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Scale className="h-3 w-3" />
                              {donation.weight} kg
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {donation.partner.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(donation.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2 text-yellow-600 font-medium">
                          <Coins className="h-4 w-4" />
                          +{donation.tokensEarned}
                        </div>
                        <Badge className={getStatusColor(donation.status)}>
                          {donation.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="partners" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {partners.map((partner) => (
              <Card key={partner._id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-red-100 rounded-full">
                      <Building className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{partner.name}</CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {partner.location}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{partner.description}</p>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1 text-gray-500">
                      <Users className="h-3 w-3" />
                      {partner.totalDonations} donations
                    </div>
                    <div className="flex items-center gap-1 text-yellow-600">
                      <Coins className="h-3 w-3" />
                      {partner.rewardRate} tokens/kg
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
