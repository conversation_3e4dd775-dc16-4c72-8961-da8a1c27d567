'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Recycle, 
  Scale, 
  Coins, 
  TrendingUp,
  Calendar,
  MapPin,
  Package,
  Award,
  Leaf,
  Building
} from 'lucide-react';

interface RecyclingStats {
  totalWeight: number;
  totalRecycling: number;
  tokensEarned: number;
  co2Saved: number;
  rank: string;
}

interface RecyclingRecord {
  _id: string;
  weight: number;
  tokensEarned: number;
  center: {
    name: string;
    location: string;
  };
  status: 'pending' | 'collected' | 'processed';
  createdAt: string;
}

interface RecyclingCenter {
  _id: string;
  name: string;
  description: string;
  location: string;
  totalRecycled: number;
  rewardRate: number;
}

export default function RecyclingPage() {
  const [stats, setStats] = useState<RecyclingStats>({
    totalWeight: 0,
    totalRecycling: 0,
    tokensEarned: 0,
    co2Saved: 0,
    rank: 'Beginner'
  });
  const [records, setRecords] = useState<RecyclingRecord[]>([]);
  const [centers, setCenters] = useState<RecyclingCenter[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecyclingData();
  }, []);

  const fetchRecyclingData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Fetch recycling stats
      const statsResponse = await fetch('/api/recycling/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      // Fetch recycling history
      const historyResponse = await fetch('/api/recycling/history', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      // Fetch recycling centers
      const centersResponse = await fetch('/api/recycling-centers');

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data || stats);
      }

      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setRecords(historyData.data || []);
      }

      if (centersResponse.ok) {
        const centersData = await centersResponse.json();
        setCenters(centersData.data || []);
      }
    } catch (error) {
      console.error('Error fetching recycling data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processed': return 'bg-green-100 text-green-800';
      case 'collected': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Recycling</h1>
          <p className="text-gray-600">Recycle old clothing and earn Pedi tokens</p>
        </div>
        <div className="text-right">
          <div className="flex items-center gap-2 text-2xl font-bold text-green-600">
            <Recycle className="h-6 w-6" />
            {stats.totalRecycling}
          </div>
          <p className="text-sm text-gray-500">Items Recycled</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalWeight} kg</div>
            <p className="text-xs text-muted-foreground">Clothing recycled</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tokens Earned</CardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.tokensEarned}</div>
            <p className="text-xs text-muted-foreground">From recycling</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CO₂ Saved</CardTitle>
            <Leaf className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.co2Saved} kg</div>
            <p className="text-xs text-muted-foreground">Environmental impact</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recycler Rank</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.rank}</div>
            <p className="text-xs text-muted-foreground">Current level</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recycle" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recycle">Schedule Pickup</TabsTrigger>
          <TabsTrigger value="history">Recycling History</TabsTrigger>
          <TabsTrigger value="centers">Recycling Centers</TabsTrigger>
        </TabsList>

        <TabsContent value="recycle" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Schedule a Recycling Pickup</CardTitle>
              <CardDescription>
                Schedule a pickup for your old clothing items and earn Pedi tokens
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Recycle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">Recycling pickup form coming soon!</p>
                <Button disabled>Schedule Pickup</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Your Recycling History</CardTitle>
              <CardDescription>Track your past recycling activities and their impact</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : records.length === 0 ? (
                <div className="text-center py-8">
                  <Recycle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No recycling records yet. Start recycling to help the environment!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {records.map((record) => (
                    <div key={record._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-green-100 rounded-full">
                          <Recycle className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">{record.center.name}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Scale className="h-3 w-3" />
                              {record.weight} kg
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {record.center.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(record.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2 text-yellow-600 font-medium">
                          <Coins className="h-4 w-4" />
                          +{record.tokensEarned}
                        </div>
                        <Badge className={getStatusColor(record.status)}>
                          {record.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="centers" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {centers.map((center) => (
              <Card key={center._id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-full">
                      <Building className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{center.name}</CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {center.location}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{center.description}</p>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1 text-gray-500">
                      <Package className="h-3 w-3" />
                      {center.totalRecycled} kg processed
                    </div>
                    <div className="flex items-center gap-1 text-yellow-600">
                      <Coins className="h-3 w-3" />
                      {center.rewardRate} tokens/kg
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
