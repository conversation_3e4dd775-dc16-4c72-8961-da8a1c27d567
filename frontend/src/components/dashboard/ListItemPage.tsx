'use client';

import { useState } from 'react';
import ClothingForm from '@/components/clothing/ClothingForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle } from 'lucide-react';

interface ClothingFormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice: string;
  originalPrice: string;
  tags: string;
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo: string;
  };
}

interface ListItemPageProps {
  onNavigate?: (page: string) => void;
}

export default function ListItemPage({ onNavigate }: ListItemPageProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (formData: ClothingFormData, images: File[]) => {
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to list an item');
      }

      // Create FormData for file upload
      const submitData = new FormData();
      
      // Add form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'sustainabilityInfo') {
          submitData.append(key, JSON.stringify(value));
        } else {
          submitData.append(key, value);
        }
      });

      // Add images
      images.forEach((image, index) => {
        submitData.append('images', image);
      });

      const response = await fetch('/api/clothing', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: submitData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to list item');
      }

      setSuccess(true);
      
      // Navigate to my listings after 2 seconds
      setTimeout(() => {
        onNavigate?.('my-listings');
      }, 2000);

    } catch (err) {
      console.error('Error listing item:', err);
      setError(err instanceof Error ? err.message : 'Failed to list item');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="max-w-md w-full">
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="space-y-2">
                <p className="font-medium">Item listed successfully!</p>
                <p className="text-sm">You earned 10 Pedi tokens. Redirecting to your listings...</p>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          List Your Clothing Item
        </h1>
        <p className="text-gray-600">
          Share your pre-loved fashion with the Pedi community and contribute to sustainable fashion
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <ClothingForm onSubmit={handleSubmit} loading={loading} />
    </div>
  );
}
