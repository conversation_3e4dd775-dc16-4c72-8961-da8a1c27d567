'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  Recycle, 
  DollarSign,
  Package,
  Shirt,
  Heart,
  Lightbulb,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  PieChart,
  Target
} from 'lucide-react';

interface WardrobeStats {
  totalItems: number;
  categories: string[];
  conditions: { [key: string]: number };
  totalValue: number;
  averageValue: number;
}

interface WardrobeInsights {
  mostCommonCategory: string;
  conditionBreakdown: { [key: string]: number };
  sustainabilityTips: string[];
}

interface WardrobeAnalysis {
  success: boolean;
  advice?: string;
  recommendations?: {
    wardrobeStats?: WardrobeStats;
    insights?: WardrobeInsights;
    tips?: string[];
    sustainabilityScore?: number;
  };
  error?: string;
}

interface WardrobeAnalysisProps {
  className?: string;
}

export function WardrobeAnalysis({ className }: WardrobeAnalysisProps) {
  const [analysis, setAnalysis] = useState<WardrobeAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateAnalysis = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to analyze your wardrobe');
      }

      const response = await fetch('/api/ai?endpoint=wardrobe-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({}),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to analyze wardrobe');
      }

      setAnalysis(result.data);
    } catch (error: any) {
      console.error('Error analyzing wardrobe:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    generateAnalysis();
  }, []);

  const handleRefresh = () => {
    generateAnalysis();
  };

  const getSustainabilityColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 70) return 'text-brand-pine-green';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSustainabilityLabel = (score?: number) => {
    if (!score) return 'Unknown';
    if (score >= 70) return 'Excellent';
    if (score >= 40) return 'Good';
    return 'Needs Improvement';
  };

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'excellent': return 'bg-brand-pine-green/10 text-brand-pine-green border border-brand-pine-green/20';
      case 'good': return 'bg-brand-dark-green/10 text-brand-dark-green border border-brand-dark-green/20';
      case 'fair': return 'bg-yellow-50 text-yellow-700 border border-yellow-200';
      case 'poor': return 'bg-red-50 text-red-700 border border-red-200';
      default: return 'bg-gray-50 text-gray-700 border border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return `KES ${amount.toLocaleString()}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Wardrobe Analysis
            </CardTitle>
            <CardDescription>
              AI-powered insights into your wardrobe and sustainability score
            </CardDescription>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Error State */}
        {error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-600" />
              <p className="text-sm text-gray-600">Analyzing your wardrobe...</p>
            </div>
          </div>
        )}

        {/* Analysis Results */}
        {analysis && !isLoading && (
          <div className="space-y-6">
            {analysis.success ? (
              <>
                {/* Sustainability Score */}
                {analysis.recommendations?.sustainabilityScore && (
                  <div className="bg-gradient-to-r from-brand-pine-green/5 to-brand-dark-green/5 rounded-lg p-4 border border-brand-pine-green/10">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-brand-dark-green flex items-center gap-2">
                        <Recycle className="h-5 w-5 text-brand-pine-green" />
                        Sustainability Score
                      </h3>
                      <Badge className={`${getSustainabilityColor(analysis.recommendations.sustainabilityScore)} bg-transparent border-current`}>
                        {getSustainabilityLabel(analysis.recommendations.sustainabilityScore)}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Your Score</span>
                        <span className={`font-medium ${getSustainabilityColor(analysis.recommendations.sustainabilityScore)}`}>
                          {analysis.recommendations.sustainabilityScore}/100
                        </span>
                      </div>
                      <Progress 
                        value={analysis.recommendations.sustainabilityScore} 
                        className="h-2"
                      />
                    </div>
                  </div>
                )}

                {/* Wardrobe Statistics */}
                {analysis.recommendations?.wardrobeStats && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Package className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Total Items</p>
                            <p className="text-xl font-bold">{analysis.recommendations.wardrobeStats.totalItems}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <DollarSign className="h-5 w-5 text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Total Value</p>
                            <p className="text-xl font-bold">{formatCurrency(analysis.recommendations.wardrobeStats.totalValue)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <PieChart className="h-5 w-5 text-purple-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Categories</p>
                            <p className="text-xl font-bold">{analysis.recommendations.wardrobeStats.categories.length}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                            <Target className="h-5 w-5 text-orange-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Avg. Value</p>
                            <p className="text-xl font-bold">{formatCurrency(analysis.recommendations.wardrobeStats.averageValue)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* AI Analysis */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <TrendingUp className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-2">AI Analysis</h3>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{analysis.advice}</p>
                    </div>
                  </div>
                </div>

                {/* Condition Breakdown */}
                {analysis.recommendations?.insights?.conditionBreakdown && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Shirt className="h-4 w-4" />
                      Condition Breakdown
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {Object.entries(analysis.recommendations.insights.conditionBreakdown).map(([condition, count]) => (
                        <div key={condition} className="text-center">
                          <Badge className={`${getConditionColor(condition)} mb-2`}>
                            {condition}
                          </Badge>
                          <p className="text-2xl font-bold text-gray-900">{count}</p>
                          <p className="text-xs text-gray-600">items</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Categories */}
                {analysis.recommendations?.wardrobeStats?.categories && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Your Categories
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {analysis.recommendations.wardrobeStats.categories.map((category, index) => (
                        <Badge key={index} variant="secondary" className="capitalize">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Sustainability Tips */}
                {analysis.recommendations?.insights?.sustainabilityTips && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Heart className="h-4 w-4 text-green-600" />
                      Sustainability Tips
                    </h3>
                    <div className="space-y-2">
                      {analysis.recommendations.insights.sustainabilityTips.map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm text-gray-700">
                          <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional Recommendations */}
                {analysis.recommendations?.tips && analysis.recommendations.tips.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Lightbulb className="h-4 w-4 text-yellow-600" />
                      Recommendations
                    </h3>
                    <div className="space-y-2">
                      {analysis.recommendations.tips.map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm text-gray-700">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {analysis.advice || 'Unable to analyze wardrobe at this time.'}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
